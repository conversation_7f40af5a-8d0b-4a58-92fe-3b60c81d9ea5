<?php

namespace App\Admin\Tracker;

use App\Admin\Exception\ErrorException;
use App\Admin\Exception\InfoException;
use App\Admin\GravitiqExportableAdminInterface;
use App\Domain\Enum\SupplyChainCategory;
use App\Entity\Sonata\User;
use App\Entity\Tracker\SalesForecastGroup;
use App\Repository\Tracker\SalesForecastGroupRepository;
use App\Service\Internal\SalesForecastManager;
use App\Tools\WrappedDoctrineIterator;
use Doctrine\ORM\Query\Expr\Join;
use Sonata\AdminBundle\Datagrid\DatagridMapper;
use Sonata\AdminBundle\Datagrid\ListMapper;
use Sonata\AdminBundle\FieldDescription\FieldDescriptionInterface;
use Sonata\AdminBundle\Filter\Model\FilterData;
use Sonata\AdminBundle\Form\FormMapper;
use Sonata\AdminBundle\Form\Type\TemplateType;
use Sonata\AdminBundle\Route\RouteCollectionInterface;
use Sonata\DoctrineORMAdminBundle\Datagrid\ProxyQueryInterface;
use Sonata\DoctrineORMAdminBundle\Filter\CallbackFilter;
use Sonata\DoctrineORMAdminBundle\Filter\ChoiceFilter;
use Sonata\DoctrineORMAdminBundle\Filter\DateRangeFilter;
use Sonata\Exporter\Source\DoctrineORMQuerySourceIterator;
use Sonata\Form\Type\CollectionType;
use Sonata\Form\Type\DatePickerType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\EnumType;

/**
 * @phpstan-extends GravitiqTrackerBaseAdmin<SalesForecastGroup>
 */
class SalesForecastGroupAdmin extends GravitiqTrackerBaseAdmin implements GravitiqExportableAdminInterface
{
    protected SalesForecastManager $sfgManager;

    protected const string ACCESS_UNCONFIRM_ANY = 'UNCONFIRM_ANY';
    protected const string ACCESS_UNCONFIRM_EARLY = 'UNCONFIRM_EARLY';

    public function hasAccessUnconfirmAny(): bool
    {
        return $this->hasAccess(self::ACCESS_UNCONFIRM_ANY);
    }
    public function hasAccessUnconfirmEarly(): bool
    {
        return $this->hasAccess(self::ACCESS_UNCONFIRM_EARLY);
    }

    public function getFootnoteContent(?string $action): ?string
    {
        if ('list' === $action) {
            return <<<HTML
                <b>Notes:</b><br>
                <ul>
                    <li>To bulk upload forecasts: [ <a href="https://share.zight.com/o0u1m15r" target="_blank" title="Watch video"><i class="fa fa-video"></i> video</a> ]</li>
                        <ol>
                            <li>filter the list view to the forecasts you want</li>
                            <li>click "all elements" at the bottom of the list (or manually select items you want)</li>
                            <li>choose "export as template" from the batch actions (at the bottom of the list)</li>
                            <li>click ok, then save and edit the Excel file</li>
                            <li>click actions > import at the top of the list page</li>
                            <li>choose your updated Excel file</li>
                            <li>check the response messages from Newton</li>
                        </ol>
                    </li>
                </ul>
            HTML;
        } elseif ('edit' === $action) {
            return <<<HTML
                <b>Notes:</b><br>
                <ul>
                    <li>Forecast can be edited until it is confirmed.</li>
                    <li>UK Supply Chain team can unlock forecasts when absolutely necessary.</li>
                </ul>
            HTML;
        }

        return null;
    }

    protected function configureActionButtons(array $buttonList, string $action, ?object $object = null): array
    {
        $buttonList['import'] = ['template' => 'sonata/crud/import_button.html.twig'];
        return $buttonList;
    }

    protected function configureRoutes(RouteCollectionInterface $collection): void
    {
        $collection->clearExcept(['list','create','edit','export','batch']);
        $collection->add('import');
    }

    protected function getAccessMapping(): array
    {
        return [
            self::ACCESS_UNCONFIRM_ANY      => ['EDIT', 'UNCONFIRM_ANY'],
            self::ACCESS_UNCONFIRM_EARLY    => ['EDIT', 'UNCONFIRM_EARLY'],
        ];
    }

    protected function configureBatchActions(array $actions): array
    {
        $actions = parent::configureBatchActions($actions);
        if ($this->hasRoute('edit') && $this->hasAccess('edit')) {
            $actions['approve'] = [
                'ask_confirmation' => true,
            ];
            $actions['exportAsTemplate'] = [
                'ask_confirmation' => false,
            ];
            $actions['setToA'] = [
                'ask_confirmation' => true,
            ];
            $actions['setToB'] = [
                'ask_confirmation' => true,
            ];
            $actions['setToC'] = [
                'ask_confirmation' => true,
            ];
            $actions['setToD'] = [
                'ask_confirmation' => true,
            ];
            $actions['setToN'] = [
                'ask_confirmation' => true,
            ];
        }

        return $actions;
    }

    public function setSalesForecastManager(SalesForecastManager $sfgManager): void
    {
        $this->sfgManager = $sfgManager;
    }
    public function getSalesForecastManager(): SalesForecastManager
    {
        return $this->sfgManager;
    }

    public function getPreviousMonthSfg(): ?SalesForecastGroup
    {
        $sfg = $this->getSubject();
        $currentForecastMadeInMonth = $sfg->getForecastMadeInMonth();
        $previousMonthDate = (clone $currentForecastMadeInMonth)->modify('-1 month');

        /** @var SalesForecastGroupRepository $sfgRepo */
        $sfgRepo = $this->getRepository(SalesForecastGroup::class);
        return $sfgRepo->retrieveSfgByIskuChannelFamilyRegionMonth(
            $sfg->getBlueprint()->getIsku(),
            $sfg->getChannelFamily(),
            $sfg->getSfRegion(),
            $previousMonthDate
        );
    }

    public function getForecastDetailTooltipMessageAndColor(?int $currentSfgFsr, ?int $previousSfgFsr): array
    {
        if (is_null($currentSfgFsr) || is_null($previousSfgFsr)) {
            return ['tooltip' => '', 'color' => 'black'];
        }

        if ($currentSfgFsr > $previousSfgFsr) {
            $diff = $currentSfgFsr - $previousSfgFsr;
            return ['tooltip' => "+$diff than previous month", 'color' => 'red'];
        } elseif ($currentSfgFsr < $previousSfgFsr) {
            $diff = $previousSfgFsr - $currentSfgFsr;
            return ['tooltip' => "-$diff than previous month", 'color' => 'green'];
        } else {
            return ['tooltip' => "Same as previous month", 'color' => 'green'];
        }
    }

    protected function configureListFields(ListMapper $list): void
    {
        $list
            ->addIdentifier('id')
            ->add('blueprint', null, [
                'label'     => 'isku'
            ])
            ->add('channelFamily', null, [
                'label'     => 'Channel',
            ])
            ->add('sfRegion', null, [
                'label'     => 'SC Region',
            ])
            ->add('supplyChainCategory', FieldDescriptionInterface::TYPE_ENUM, [
                'label'     => 'SC Category',
            ])
            ->add('forecastMadeInMonth', null, ['label'=>'Forecast from', 'format'=>'Y-m'])
            ->add('caFourMonthsDelta')
            ->add('forecastConfirmationDate', null, [
                'label' =>'Confirmed on',
                'format' =>'Y-m-d',
                'template' => 'sonata/crud/list_field_unconfirmable_datetime.html.twig'
            ])
            ->add('sfDetailsWithGroupAsSfg', null, [
                'label'     => 'Forecast',
                'template'  => 'sonata/crud/tracker/list_sf_details.html.twig'
            ])
        ;
    }

    protected function configureExportFields(): array
    {
        return [
            'brandCode', 'blueprint.isku', 'asinsString', 'sfRegion', 'channelFamily', 'supplyChainCategoryString', 'forecastMadeInMonth', 'isConfirmed',
            'hsr0', 'hsr1', 'hsr2', 'hsr3', 'hsr4', 'hsr5', 'hsr6', 'hsr7', 'hsr8', 'hsr9', 'hsr10', 'hsr11',
            'fsr1', 'fsr2', 'fsr3', 'fsr4', 'fsr5', 'fsr6', 'fsr7', 'fsr8', 'fsr9', 'fsr10', 'fsr11', 'fsr12',
            'comment1', 'comment2', 'comment3', 'comment4', 'comment5', 'comment6', 'comment7', 'comment8', 'comment9', 'comment10', 'comment11', 'comment12',
        ];
    }

    public function getCustomDataSourceIterator(): \Iterator
    {
        /** @var DoctrineORMQuerySourceIterator $standardIterator */
        $standardIterator = $this->getDataSourceIterator();

        $transformers = [
            'setSalesForecastManager' => $this->sfgManager,
        ];

        return new WrappedDoctrineIterator($standardIterator, $transformers, ['notUsed']);
    }

    protected function configureDefaultFilterValues(array &$filterValues): void
    {
        parent::configureDefaultFilterValues($filterValues);

        $defaultDate = new \DateTimeImmutable('first day of this month');
        $filterValues['forecastMadeInMonth']['value'] = $defaultDate->format('Y-m-d');
    }

    protected function configureDatagridFilters(DatagridMapper $filter): void
    {
        $filter
            ->add('blueprint.brandCode', CallbackFilter::class, [
                'label'             => 'BrandCode',
                'callback'          => static function(ProxyQueryInterface $query, string $alias, string $field, FilterData $data): bool {
                    if (!$data->hasValue()) {
                        return false;
                    }

                    $qb = $query->getQueryBuilder();

                    $codes = explode(',', $data->getValue());
                    $orClauses = [];
                    foreach ($codes as $key => $code) {
                        $code = trim($code) . '%';
                        $orClauses[] = $qb->expr()->like("$alias.isku", ":code{$key}");
                        $qb->setParameter("code{$key}", $code);
                    }
                    $qb
                        ->andWhere($qb->expr()->orX(...$orClauses))
                    ;

                    return true;
                },
            ])
            ->add('blueprint.isku')
            ->add('cASIN', CallbackFilter::class, [
                'label' => 'cASIN',
                'callback'          => static function(ProxyQueryInterface $query, string $alias, string $field, FilterData $data): bool {
                    if (!$data->hasValue()) {
                        return false;
                    }

                    $qb = $query->getQueryBuilder();
                    $asinFragment = $data->getValue();

                    $qb
                        ->leftJoin("$alias.blueprint", 'b')
                        ->leftJoin("b.channelSkus", 'cs', Join::WITH, $qb->expr()->like('cs.uid', ':asinFragment'))
                        ->leftJoin("cs.channel", 'c', Join::WITH, $qb->expr()->eq('c.sfRegion', "$alias.sfRegion"))
                        ->setParameter('asinFragment', "%{$asinFragment}%")
                        ->andWhere($qb->expr()->isNotNull('c.id'))
                    ;

                    return true;
                },
            ])
            ->add('channelFamily', null, ['label' => 'Channel'])
            ->add('sfRegion', null, ['label' => 'SC Region'])
            ->add('supplyChainCategory', ChoiceFilter::class, [
                'label'             => 'SC Category',
                'field_type'        => EnumType::class,
                'field_options'     => [
                    'multiple'      => true,
                    'class'         => SupplyChainCategory::class,
                ]
            ])
            ->add('forecastMadeInMonth', CallbackFilter::class, [
                'field_type'        => DatePickerType::class,
                'datepicker_options' => ['useCurrent' => true],
                'label'             => 'Forecast from',
                'field_options'     => [
                    'format'    => DateType::HTML5_FORMAT,
                ],
                'show_filter'       => false,
                'callback'          => static function(ProxyQueryInterface $query, string $alias, string $field, FilterData $data): bool {
                    if (!$data->hasValue()) {
                        return false;
                    }

                    $qb = $query->getQueryBuilder();
                    $minDate = new \DateTimeImmutable($data->getValue()->format('Y-m-01 00:00:00'));
                    $maxDate = $minDate->modify('+1 month');

                    $qb
                        ->andWhere($qb->expr()->gte("$alias.forecastMadeInMonth", ':minMIMDate'))
                        ->andWhere($qb->expr()->lt("$alias.forecastMadeInMonth", ':maxMIMDate'))
                        ->setParameter('minMIMDate', $minDate)
                        ->setParameter('maxMIMDate', $maxDate)
                    ;

                    return true;
                },
            ])
            ->add('forecastConfirmationDate', DateRangeFilter::class, $this->getStandardDateRangeFilterOptions())
            ->add('unconfirmed')
            ->add('isConfirmed', CallbackFilter::class, [
                'field_type'        => ChoiceType::class,
                'label'             => 'Is Confirmed?',
                'field_options'     => [
                    'choices'       => [
                        'Yes'   => 'YES',
                        'No'    => 'NO',
                    ],
                ],
                'callback'          => static function(ProxyQueryInterface $query, string $alias, string $field, FilterData $data): bool {
                    if (!$data->hasValue()) {
                        return false;
                    }

                    $qb = $query->getQueryBuilder();

                    if ($data->getValue() === 'YES') {
                        $qb->andWhere($qb->expr()->isNotNull("$alias.forecastConfirmationDate"));
                    } else {
                        $qb->andWhere($qb->expr()->isNull("$alias.forecastConfirmationDate"));
                    }

                    return true;
                },
            ])
       ;
    }

    protected function configureFormFields(FormMapper $form): void
    {
        $editingDisabled = $this->getSubject()->isTooLateToEditAnyForecast() || $this->getSubject()->isTooLateToEditConfirmedForecast();

        $form
            ->with('Forecast', ['class' => 'col-md-6 with-sfg-forecast'])
                ->add('sfDetails', CollectionType::class, [
                    'required'      => true,
                    'disabled'      => $editingDisabled,
                    'by_reference'  => false,
                    'label'         => false,
                    'row_attr'=>[
                        'data-controller' => 'gq-sfg-chart',
                    ],
                    'btn_add'=>'Add item',
                    'type_options' => [
                        'delete' => false,
                    ],
                ],[
                    'edit' => 'inline',
                    'inline' => 'table:template:sonata/crud/association/edit_one_to_many_inline_table.modified.html.twig',
                ])
            ->end()
            ->with('Chart', ['class' => 'col-md-6'])
                ->add('forecastModelChart', TemplateType::class, [
                    'template'   => 'sonata/crud/tracker/sfg_forecast_chart.html.twig',
                    'parameters' => [
                        'forecastModelChart' => $this->sfgManager->buildChartForForecastModels($this->getSubject()),
                    ],
                ])
            ->end()
            ->with('ASINs', ['class' => 'col-md-6 gq-no-titles'])
                ->add('Links', TemplateType::class, [
                    'template'   => 'sonata/crud/tracker/channel_links.html.twig',
                    'parameters' => [
                        'channelSkus' => $this->sfgManager->buildChannelSkuListForForecastGroup($this->getSubject()),
                    ],
                ])
            ->end()
            ->with('Main details', ['class' => 'col-md-6 clear-left'])
                ->add('blueprint', null, [
                    'disabled' => true,
                    'required' => true,
                    'row_attr' => self::ROW_ATTR_HALF_WIDTH_FIELD_LEFT,
                ])
                ->add('sfRegion', null, [
                    'disabled' => true,
                    'required' => true,
                    'row_attr' => self::ROW_ATTR_HALF_WIDTH_FIELD_RIGHT,
                ])
                ->add('forecastMadeInMonth', null, [
                    'disabled' => true,
                    'widget'   => 'single_text',
                    'format'   => 'MMM yyyy',
                    'html5'    => false,
                    'help'     => 'Set automatically when forecast is saved',
                    'row_attr' => self::ROW_ATTR_HALF_WIDTH_FIELD_LEFT,
                ])
                ->add('supplyChainCategory', EnumType::class, [
                    'required' => true,
                    'class'    => SupplyChainCategory::class,
                    'row_attr' => self::ROW_ATTR_HALF_WIDTH_FIELD_RIGHT,
                ])
                ->add('channelFamily', null, [
                    'disabled' => true,
                    'required' => true,
                    'row_attr' => self::ROW_ATTR_HALF_WIDTH_FIELD_RIGHT,
                ])
            ->end()
            ->with('Ownership', ['class' => 'col-md-6'])
                ->add('createdByUser', null, [
                    'label'    => 'Updated By User',
                    'disabled' => true,
                    'required' => false,
                    'row_attr' => self::ROW_ATTR_THIRD_WIDTH_FIELD_LEFT,
                ])
                ->add('confirmedByUser', null, [
                    'disabled' => true,
                    'required' => false,
                    'help'     => 'Set when forecast is confirmed',
                    'row_attr' => self::ROW_ATTR_THIRD_WIDTH_FIELD_MID,
                ])
                ->add('forecastConfirmationDate', null, [
                    'disabled' => true,
                    'required' => false,
                    'help'     => 'Set automatically when forecast is confirmed',
                    'widget'   => 'single_text',
                    'format'   => 'yyyy-MM-dd',
                    'html5'    => false,
                    'row_attr' => self::ROW_ATTR_THIRD_WIDTH_FIELD_RIGHT,
                ])
            ->end()
        ;

        $confirmLabel = $this->getSubject()->getForecastConfirmationDate() ? 'Reconfirm forecast' : 'Confirm forecast';
        $form
            ->with('Ownership', ['class' => 'col-md-6'])
                ->add('doConfirmation', CheckboxType::class, [
                    'required' => false,
                    'label'    => $confirmLabel,
                ])
            ->end()
        ;
        if ($this->getSubject()->isPossibleToUnconfirm($this->hasAccessUnconfirmEarly(), $this->hasAccessUnconfirmAny())) {
            $form
                ->with('Ownership')
                    ->add('unconfirmed', CheckboxType::class, [
                        'required' => false,
                        'label'    => 'Unconfirm to allow editing',
                    ])
                ->end()
            ;
        }
    }

    protected function preUpdate(object $object): void
    {
        $user = $this->getTokenStorage()->getToken()->getUser();
        if (!$user instanceof User) {
            return;
        }
        /** @var SalesForecastGroup $object */
        $object->setCreatedByUser($user);
        if ($object->doConfirmation) {
            $object->setConfirmedByUser($user);
            $object->setUnconfirmed(false);
            $object->setForecastConfirmationDateToNow();
        }
    }

    /**
     * @param SalesForecastGroup $object
     * @return void
     * @throws ErrorException|InfoException
     */
    public function throwExceptionIfSalesForecastGroupIsNotEditable(SalesForecastGroup $object): void
    {
        if (!$object->isMadeInCurrentMonth()) {
            throw new ErrorException("Forecast #{$object->getId()} is not made in the current month. You can only edit SFGs made in the current month");
        }

        if ($object->isTooLateToEditAnyForecast()) {
            if ($object->isPossibleToUnconfirm($this->hasAccessUnconfirmEarly(), $this->hasAccessUnconfirmAny())) {
                throw new InfoException("Forecast #{$object->getId()} can't be edited. To allow the brand team to edit this forecast tick unconfirm (then save)");
            } else {
                throw new ErrorException("You cannot edit Forecast #{$object->getId()} after the deadline. To change the values, first ask a Supply Chain Admin to unconfirm the Forecast");
            }
        } elseif ($object->isTooLateToEditConfirmedForecast()) {
            if ($object->isPossibleToUnconfirm($this->hasAccessUnconfirmEarly(), $this->hasAccessUnconfirmAny())) {
                throw new InfoException("Forecast #{$object->getId()} is confirmed, but you can unconfirm it to allow it to be edited again");
            } else {
                throw new ErrorException("You cannot edit confirmed Forecast #{$object->getId()} after the deadline. To change the values, first ask a UK Supply Chain Admin to unconfirm the Forecast");
            }
        }
    }
}