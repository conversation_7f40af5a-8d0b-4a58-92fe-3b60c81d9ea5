<?php

namespace App\DataFixtures\Service\Internal;

use App\DataFixtures\GravitiqFixture;
use Doctrine\ORM\EntityManager;
use Doctrine\Persistence\ObjectManager;

class SalesForecastManagerFixtures extends GravitiqFixture
{
    const int FSR_GROUP_ID1 = 100;
    const int FSR_DETAIL_ID1 = 101;
    const int FSR_DETAIL_ID2 = 102;

    public static function getFsrGroupId1(): int
    {
        return self::FSR_GROUP_ID1;
    }

    /**
     * @return array{float, float, float, recentDsr: array<string, float>}
     */
    public static function getExpectedSalesHistory(): array
    {
        return [
            0 => 4.0,
            1 => 5.0,
            2 => 0.0,
            "recentDsr" => [
                "7d" => 0.0,
                "30d" => 0.0,
            ],
        ];
    }

    /**
     * @param EntityManager $manager
     * @return void
     * @throws \DateMalformedStringException
     */
    public function load(ObjectManager $manager): void
    {
        $now = new \DateTimeImmutable();
        $firstDayLastMonth = $now->modify('first day of last month')->format('Y-m-01');
        $lastDayLastMonth = $now->modify('last day of last month')->format('Y-m-t');
        $firstDayTwoMonthsAgo = $now->modify('-2 months')->modify('first day of this month')->format('Y-m-01');
        $lastDayTwoMonthsAgo = $now->modify('-2 months')->modify('last day of this month')->format('Y-m-t');
        $daysInLastMonth = (int)$now->modify('-1 months')->format('t');
        $daysInTwoMonthsAgo = (int)$now->modify('-2 months')->format('t');
        $dsr2PerDayLastMonth = 2 * $daysInLastMonth;
        $dsr3PerDayLastMonth = 3 * $daysInLastMonth;
        $dsr4PerDayTwoMonthsAgo = 4 * $daysInTwoMonthsAgo;
        $fsrDetailId1 = self::FSR_DETAIL_ID1;
        $fsrDetailId2 = self::FSR_DETAIL_ID2;

        $fsrGroupId1 = self::getFsrGroupId1();

        $sqlStatements = [
            <<<EOF

    INSERT INTO top_channel_sku (`id`, `channelId`, `parentChannelSkuId`, `uid`, `buyable`, `brand`, `productName`)
    VALUES 
        (101, 'AMZ_US', NULL, 'B07D7J3L3F', 1, NULL, NULL),
        (102, 'AMZ_US', NULL, 'B000001J4W', 1, NULL, NULL);

    INSERT INTO tk_join_blueprint_channel_sku (`csId`, `blueprintId`)
    VALUES 
        (101, 1),
        (102, 1);

    INSERT INTO tk_fsr_group (`id`, `blueprintId`, `createdByUserId`, `confirmedByUserId`, `channelFamily`, `sfRegion`, `supplyChainCategory`, `forecastMadeInMonth`, `forecastConfirmationDate`)
    VALUES 
        ({$fsrGroupId1}, 1, 1, 1, 'AMZ', 'US', 'A', '{$firstDayLastMonth}', NULL);

    INSERT INTO tk_fsr_detail (`id`, `sfGroupId`, `forecastIsForMonth`, `fsr`, `basedOnModel`, `deltaPct`, `deltaCalc`, `fsrPct`, `notes`, `ordinal`)
    VALUES 
        ({$fsrDetailId1}, {$fsrGroupId1}, '{$firstDayLastMonth}', 0.0, 'A', 0, 0.0, 0, '', 1),
        ({$fsrDetailId2}, {$fsrGroupId1}, '{$firstDayTwoMonthsAgo}', 0.0, 'A', 0, 0.0, 0, '', 2);

    INSERT INTO amazon_report_spa (`id`, `accountProfileId`, `marketplace`, `reportType`, `status`, `recordCount`, `savedFileSize`, `scheduled`, `requestDate`, `parseDate`, `startDate`, `endDate`, `azReportId`, `azReportDocumentId`, `fileSystemName`, `filename`, `reportOptions`, `error`)
    VALUES 
        (101, 2, 'US', 'GET_SALES_AND_TRAFFIC_REPORT', 'DONE', 0, 0, 0, '2024-02-07 00:00:00', '2024-02-07 00:00:00', NULL, NULL, '123', '123', 'amazonReportsLocal', NULL, NULL, NULL);

    INSERT INTO data_spa_rep_sra_sat_asin (`id`, `spaProfileId`, `reportId`, `startDate`, `endDate`, `parentAsin`, `childAsin`, `salesValue`, `salesValueB2b`, `salesOrders`, `salesOrdersB2b`, `salesUnits`, `salesUnitsB2b`, `pageViewsBrowser`, `pageViewsApp`, `sessionsBrowser`, `sessionsApp`, `buyBoxPct`)
    VALUES
        (101, 2, 101, '{$firstDayLastMonth}',    '{$lastDayLastMonth}',    'B01F7O8KG4', 'B07D7J3L3F', 100, 0, 0, 0, $dsr2PerDayLastMonth,    0, 0, 0, 0, 0, 100.0),
        (102, 2, 101, '{$firstDayLastMonth}',    '{$lastDayLastMonth}',    'B01ICPY9SS', 'B000001J4W', 200, 0, 0, 0, $dsr3PerDayLastMonth,    0, 0, 0, 0, 0, 100.0),
        (103, 2, 101, '{$firstDayTwoMonthsAgo}', '{$lastDayTwoMonthsAgo}', 'B01ICPY9SS', 'B000001J4W', 400, 0, 0, 0, $dsr4PerDayTwoMonthsAgo, 0, 0, 0, 0, 0, 100.0);

    INSERT INTO top_merchant_sku 
        (id, channelSkuId, merchantId, blueprintId, caChannel, caSfRegion, sku, varType, fulfilmentType, conditionCode, onSale, restockable, createDate) VALUES
        (101, 101, 2, 1, 'AMZ_US', 'US', 'SKU_AMZ_1', 'S', 'FBA', 11, 1, 1, now())
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }

    public static function loadWalmartMerchantSku(EntityManager $manager): void
    {
        $sqlStatements = [
            <<<EOF

    INSERT INTO top_channel (id, family, primaryCountry, sfRegion, internalName, website, productUrl)
    VALUES 
        ('WAL_US', 'Walmart', 'US', 'US', 'Walmart.com US', 'https://www.walmart.com', 'https://www.walmart.com/ip/%uid%');

    INSERT INTO top_channel_sku (`id`, `channelId`, `parentChannelSkuId`, `uid`, `buyable`, `brand`, `productName`)
    VALUES 
        (111, 'WAL_US', NULL, '123456', 1, NULL, NULL);

    INSERT INTO tk_join_blueprint_channel_sku (`csId`, `blueprintId`)
    VALUES 
        (111, 1);

    INSERT INTO top_merchant (`id`, `alias`, `channelId`, `internalName`, `externalName`)
    VALUES 
        (111, 'XX_WAL_US', 'WAL_US', 'XX Walmart', 'XX Walmart');

    INSERT INTO top_merchant_sku 
        (id, channelSkuId, merchantId, blueprintId, caChannel, caSfRegion, sku, varType, fulfilmentType, conditionCode, onSale, restockable, createDate) VALUES
        (111, 111, 111, 1, 'WAL_US', 'US', 'SKU_WAL_1', 'S', 'FBW', 11, 1, 1, now())
EOF,
        ];

        self::runSql($sqlStatements, $manager);
    }
}