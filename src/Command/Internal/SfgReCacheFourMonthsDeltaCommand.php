<?php

namespace App\Command\Internal;

use App\Command\GravitiqDoctrineLoggingCommand;
use App\Entity\Tracker\SalesForecastGroup;
use App\Repository\Tracker\SalesForecastGroupRepository;
use App\Service\Internal\SalesForecastManager;
use App\Tools\GravitiqTools;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'newton:sfg:recache-delta',
    description: 'Re-calculates the delta values for the last 4 months of SFGs',
)]
class SfgReCacheFourMonthsDeltaCommand extends GravitiqDoctrineLoggingCommand
{
    protected LoggerInterface $logger;

    public function __construct(
        protected SalesForecastManager $sfManager,
        LoggerInterface $gravitiqConsoleLogger,
        ManagerRegistry $doctrine)
    {
        $this->logger = $gravitiqConsoleLogger;
        $this->sfManager->setLogger($gravitiqConsoleLogger);

        parent::__construct($doctrine, $gravitiqConsoleLogger);
    }

    protected function doConfigure(): void
    {
        $this
            ->addArgument('limit', InputArgument::REQUIRED, 'The max number of new SFGs to recache')
            ->addOption('month', null, InputOption::VALUE_REQUIRED, 'The month to recache', null)
        ;
    }

    protected function doExecute(InputInterface $input, OutputInterface $output): void
    {
        $limit = $input->getArgument('limit');
        $month = $input->getOption('month');

        if (false === is_null($month)) {
            $month = GravitiqTools::castToDateTimeImmutable($month)->format('Y-m-01');
            $this->logger->info("Recaching delta for month $month");
        }

        /** @var SalesForecastGroupRepository $sfgRepo */
        $sfgRepo = $this->em->getRepository(SalesForecastGroup::class);
        $sfgs = $sfgRepo->retrieveAllSfgsForGivenMonth($month);

        foreach ($sfgs as $index => $sfg) {
            if ($index >= $limit) {
                $this->logger->info("Reached limit of $limit SFGs to recache");
                break;
            }
            $this->sfManager->updateCaFourMonthsDelta($sfg);
        }

        $this->em->flush();
    }
}