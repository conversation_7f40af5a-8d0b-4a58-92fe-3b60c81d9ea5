<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250520180710 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add caFourMonthsDelta to tk_fsr_group';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tk_fsr_group ADD caFourMonthsDelta NUMERIC(10, 2) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE tk_fsr_group DROP caFourMonthsDelta');
    }
}
