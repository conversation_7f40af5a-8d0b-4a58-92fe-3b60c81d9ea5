{# Renders the plain value, without links or other decoration #}
{% extends '@SonataAdmin/CRUD/base_list_field.html.twig' %}

{% block field %}
    {% set previousForecast = admin.getPreviousMonthSfg() %}

    {% if not value.sfg.isChannelFamilyAmazon %}
        {% apply spaceless %}
            <table class="fsrListTable" style="float:left;">
                <tr>
                    <td style="width: 4.2em;">Forecast:</td>
                    {% for i in 1..12 %}
                        <td>0</td>
                    {% endfor %}
                </tr>
            </table>
        {% endapply %}
    {% else %}
        {% apply spaceless %}
            {% set history = admin.salesForecastManager.retrieveSalesHistoryForSfg(value.sfg) %}
            <table class="fsrListTable" style="float:left;">
                <tr>
                    <td style="width: 4.2em;">Last year:</td>
                    {% if history %}
                        {% for item in history %}
                            {% if item >= 10 %}{% set roundTo = 0 %}{% else %}{% set roundTo = 1 %}{% endif %}
                            <td>{{ item|round(roundTo) }}</td>
                        {% endfor %}
                    {% else %}
                        <td colspan="12" style="text-align: left; padding-left: 1em;">Blueprint not mapped?</td>
                    {% endif %}
                </tr><tr>
                    <td style="width: 4.2em;">Forecast:</td>
                    {% for item in value.forecast %}
                        {% set i = loop.index %}
                        {% if i <= 4 %}
                            {% set previousForecastForThisMonth = previousForecast ? previousForecast.getFsrForFutureMonthIndex(i + 1) : null %}
                            {% set forecastStyle = admin.getForecastDetailTooltipMessageAndColor(item, previousForecastForThisMonth) %}
                            <td style="color: {{ forecastStyle.color }}; font-weight: bold;" title="{{ forecastStyle.tooltip }}">{{ item }}</td>
                        {% else %}
                            <td>{{ item }}</td>
                        {% endif %}
                    {% endfor %}
                </tr>
            </table>
        {% endapply %}
    {% endif %}
    <div style="width:100px; float:left;">
        {{ render_chart(admin.salesForecastManager.buildInlineChartFromSfDetailsWithGroupAsSfg(value), {'class': 'fsr-inline-chart'}) }}
    </div>
{% endblock %}
