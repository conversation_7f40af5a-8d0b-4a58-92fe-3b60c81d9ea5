<?php

namespace App\Tests\Integration\Service\Internal;

use App\Admin\Tracker\SalesForecastGroupAdmin;
use App\DataFixtures\Service\Internal\SalesForecastManagerFixtures;
use App\Entity\Sonata\User;
use App\Entity\Tracker\SalesForecastDetail;
use App\Entity\Tracker\SalesForecastGroup;
use App\Repository\Tracker\SalesForecastGroupRepository;
use App\Service\Internal\SalesForecastManager;
use App\Service\Internal\SalesRateReportBuilder;
use App\Tests\Integration\DoctrineTransactionUnitTest;
use App\Tests\Trait\CanInvokeProtectedMethodTrait;
use App\Tools\GravitiqTools;
use Codeception\Attribute\DataProvider;
use PhpOffice\PhpSpreadsheet\Calculation\Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpFoundation\Session\Storage\MockArraySessionStorage;

class SalesForecastManagerTest extends DoctrineTransactionUnitTest
{
    use CanInvokeProtectedMethodTrait;

    protected SalesForecastManager $manager;
    protected SalesRateReportBuilder $salesRateReportBuilder;
    protected SalesForecastGroupRepository $sfgRepository;

    protected function _before(): void
    {
        parent::_before();

        /** @var SalesForecastManager $manager */
        $manager = $this->grabService(SalesForecastManager::class);
        $this->manager = $manager;

        /** @var SalesRateReportBuilder $salesRateReportBuilder */
        $salesRateReportBuilder = $this->grabService(SalesRateReportBuilder::class);
        $this->salesRateReportBuilder = $salesRateReportBuilder;

        /** @var SalesForecastGroupRepository $sfgRepository */
        $sfgRepository = $this->getRepository(SalesForecastGroup::class);
        $this->sfgRepository = $sfgRepository;
    }

    protected function getFixtureClasses() :array
    {
        return [
            SalesForecastManagerFixtures::class,
        ];
    }

    public function testRetrieveSalesHistoryForSfg(): void {
        $sfg = $this->sfgRepository->find(SalesForecastManagerFixtures::getFsrGroupId1());
        $salesHistory = $this->manager->retrieveSalesHistoryForSfg($sfg, 2, true);

        $expectedSalesHistory = SalesForecastManagerFixtures::getExpectedSalesHistory();
        $this->assertIsArray($salesHistory, "The sales history should be an array.");
        $this->assertCount(count($expectedSalesHistory), $salesHistory, "The sales history array does not contain the expected number of elements.");

        // Test individual elements for more granular feedback
        foreach ($expectedSalesHistory as $key => $expectedValue) {
            $this->assertArrayHasKey($key, $salesHistory, "Key '{$key}' is missing in the sales history.");
            if (is_array($expectedValue)) {
                // If the expected value is an array, assert its structure and values separately
                $this->assertIsArray($salesHistory[$key], "Expected an array at key '{$key}'.");
                foreach ($expectedValue as $nestedKey => $nestedValue) {
                    $this->assertEquals($nestedValue, $salesHistory[$key][$nestedKey], "Value mismatch for '{$nestedKey}' in the sales history under '{$key}'.");
                }
            } else {
                // Direct value comparison for non-array elements
                $this->assertEquals($expectedValue, $salesHistory[$key], "Value mismatch at key '{$key}' in the sales history.");
            }
        }
    }

    public function testBuildDataArray(): void
    {
        $dataArray = $this->invokeProtectedMethod($this->salesRateReportBuilder, 'buildDataArray', ['ZZ_US', true]);

        $this->assertNotNull($dataArray, 'Data array should not be null.');
        $this->assertIsArray($dataArray, 'Data array should be an array.');
        $this->assertArrayHasKey('Data', $dataArray, 'Data array should have a "Data" key.');

        $data = $dataArray['Data'];
        $this->assertIsArray($data, '"Data" should be an array.');
        $this->assertCount(3, $data, '"Data" array should contain 3 items.');

        $headerRow = $data[0];
        $this->assertEquals('childAsin', $headerRow[0], 'First header should be "childAsin".');

        $specificRow = $data[1];
        $this->assertEquals('US', $specificRow['countryCode'], 'countryCode should match.');
    }

    public function testCreateSfgForListedBlueprintsWithoutThem(): void
    {
        $entityCounts = [
            SalesForecastGroup::class => $this->retrieveCountOfEntity(SalesForecastGroup::class),
        ];

        $this->manager->createSfgForListedBlueprintsWithoutThem(10);
        $entityCounts = $this->assertEntityCountsAreAdjusted($entityCounts, [
            SalesForecastGroup::class => 1, // The fixture SFGs are all for LAST month, so a new one should be created for THIS month
        ]);

        SalesForecastManagerFixtures::loadWalmartMerchantSku($this->em);
        $this->manager->createSfgForListedBlueprintsWithoutThem(10);
        $this->assertEntityCountsAreAdjusted($entityCounts, [
            SalesForecastGroup::class => 1,
        ]);
    }

    /**
     * @param string $filePath
     * @param array<string>|null $expectedHeader
     * @param string|null $expectedException
     * @param string|null $expectedMessage
     * @return void
     */
    #[DataProvider('dataProviderForGetXlsxImportHeader')]
    public function testGetXlsxImportHeader(string $filePath, ?array $expectedHeader, ?string $expectedException = null, ?string $expectedMessage = null): void
    {
        $spreadsheet = IOFactory::load($filePath);

        if ($expectedException) {
            $this->expectException($expectedException);
            $this->expectExceptionMessage($expectedMessage);
        }

        $result = $this->invokeProtectedMethod($this->manager, 'getXlsxImportHeader', [$spreadsheet]);

        if ($expectedHeader !== null) {
            $this->assertIsArray($result, 'The result should be an array.');
            $this->assertCount(count($expectedHeader), $result, 'The result should contain ' . count($expectedHeader) . ' elements.');
            $this->assertEquals($expectedHeader, $result, 'The result should match the expected header.');
        }
    }

    /**
     * @return list<array{
     *     filePath: string,
     *     expectedHeader: array<string>|null,
     *     expectedException: string|null,
     *     expectedMessage: string|null
     * }>
     */
    public function dataProviderForGetXlsxImportHeader(): array
    {
        // dynamically generate expected header from next month to +12 months
        $expectedHeader = ['Blueprint', 'Channel', 'Region', 'Category'];
        $today = new \DateTimeImmutable();
        for ($i = 1; $i <= 12; $i++) {
            $expectedHeader[] = $today->modify("+{$i} month")->format('Y-m');
        }
        $expectedHeader[] = 'Is Confirmed';

        return [
            [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-test001.xlsx',
                'expectedHeader' => $expectedHeader,
                'expectedException' => null,
                'expectedMessage' => null,
            ],
            [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-empty.xlsx',
                'expectedHeader' => null,
                'expectedException' => \RuntimeException::class,
                'expectedMessage' => 'Header row is empty',
            ],
        ];
    }

    #[DataProvider('dataProviderForForecastMadeInMonthDataProvider')]
    public function testExtractForecastMadeInMonth(string $filePath, \DateTimeImmutable $date, ?string $expectedException, ?string $expectedMessage, ?\DateTimeImmutable $expectedForecastDate): void
    {
        $spreadsheet = IOFactory::load($filePath);
        if ($expectedException) {
            $this->expectException($expectedException);
            $this->expectExceptionMessage($expectedMessage);
        }

        $forecastDate = $this->invokeProtectedMethod($this->manager, 'extractForecastMadeInMonth', [$date, $spreadsheet]);

        if ($expectedForecastDate) {
            $this->assertEquals($expectedForecastDate, $forecastDate, 'The extracted forecast date should match the expected date.');
        }
    }

    /**
     * @return array<string, array{
     *     filePath: string,
     *     date: \DateTimeImmutable,
     *     expectedException: string|null,
     *     expectedMessage: string|null,
     *     expectedForecastDate: \DateTimeImmutable|null
     * }>
     */
    public function dataProviderForForecastMadeInMonthDataProvider(): array
    {
        $validDate = GravitiqTools::castToDateTimeImmutable('midnight first day of this month');

        return [
            'valid forecast date' => [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-test001.xlsx',
                'date' => $validDate,
                'expectedException' => null,
                'expectedMessage' => null,
                'expectedForecastDate' => $validDate,
            ],
            'invalid forecast date' => [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-test002.xlsx',
                'date' => $validDate,
                'expectedException' => \RuntimeException::class,
                'expectedMessage' => 'Forecast made in month is not the same as the expected month',
                'expectedForecastDate' => null,
            ],
            'empty forecast date' => [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-empty.xlsx',
                'date' => $validDate,
                'expectedException' => \RuntimeException::class,
                'expectedMessage' => 'Forecast made in month is null',
                'expectedForecastDate' => null,
            ],
        ];
    }

    /**
     * @param string $filePath
     * @param array<string> $header
     * @param array<string, string>|null $expectedResult
     * @param string|null $expectedException
     * @param string|null $expectedMessage
     * @return void
     */
    #[DataProvider('dataProviderForExtractImportedXlsxData')]
    public function testExtractImportedXlsxData(string $filePath, array $header, ?array $expectedResult, ?string $expectedException = null, ?string $expectedMessage = null): void
    {
        $spreadsheet = IOFactory::load($filePath);

        if ($expectedException) {
            $this->expectException($expectedException);
            $this->expectExceptionMessage($expectedMessage);
        }

        $result = $this->invokeProtectedMethod($this->manager, 'extractImportedXlsxData', [$spreadsheet, $header]);

        if ($expectedResult !== null) {
            $this->assertIsArray($result, 'The result should be an array.');
            $this->assertCount(count($expectedResult), $result, 'The result should contain ' . count($expectedResult) . ' elements.');
            $this->assertEquals($expectedResult, $result, 'The result should match the expected data.');
        }
    }

    /**
     * @return list<array{
     *     filePath: string,
     *     header: array<string>,
     *     expectedResult: array<string, string>|null,
     *     expectedException: string|null,
     *     expectedMessage: string|null
     * }>
     */
    public function dataProviderForExtractImportedXlsxData(): array
    {
        $header = ["Blueprint", "Channel", "Region", "Category", "2024-10", "2024-11", "2024-12", "2025-01", "2025-02", "2025-03", "2025-04", "2025-05", "2025-06", "2025-07", "2025-08", "2025-09", "Is Confirmed"];

        return [
            [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-test001.xlsx',
                'header' => $header,
                'expectedResult' => [
                    ["Blueprint" => "XX-TEST-01", "Channel" => "AMZ", "Region" => "US", "Category" => "A", "2024-10" => 9, "2024-11" => 8, "2024-12" => 7, "2025-01" => 6, "2025-02" => 5, "2025-03" => 4, "2025-04" => 3, "2025-05" => 2, "2025-06" => 1, "2025-07" => 0, "2025-08" => 1, "2025-09" => 2, "Is Confirmed" => null]
                ],
                'expectedException' => null,
                'expectedMessage' => null,
            ],
            [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-empty.xlsx',
                'header' => $header,
                'expectedResult' => null,
                'expectedException' => \RuntimeException::class,
                'expectedMessage' => 'Spreadsheet contains no data',
            ],
        ];
    }

    /**
     * @param array<string, string> $row
     * @param string|null $expectedException
     * @param string|null $expectedMessage
     * @return void
     */
    #[DataProvider('dataProviderForThrowExceptionIfXlsxRowIsNotValid')]
    public function testThrowExceptionIfXlsxRowIsNotValid(array $row, ?string $expectedException, ?string $expectedMessage): void
    {
        $fakeRegions = ['US', 'DE'];

        if ($expectedException && $expectedMessage) {
            $this->expectException($expectedException);
            $this->expectExceptionMessage($expectedMessage);
        }

        $this->invokeProtectedMethod($this->manager, 'throwExceptionIfXlsxRowIsNotValid', [$fakeRegions, $row, 0]);
    }

    /**
     * @return list<array{
     *     row: array<string, string>,
     *     expectedException: string|null,
     *     expectedMessage: string|null
     * }>
     */
    public function dataProviderForThrowExceptionIfXlsxRowIsNotValid(): array
    {
        return [
            [
                "row" => ["Blueprint" => "XX-TEST-01", "Channel" => "AMZ", "Region" => "US", "Category" => "A"],
                "expectedException" => null,
                "expectedMessage" => null,
            ],
            [
                "row" => ["Blueprint" => "XX-TEST-01", "Channel" => "AMZ", "Region" => "TEST", "Category" => "A"],
                "expectedException" => \InvalidArgumentException::class,
                "expectedMessage" => "Invalid region TEST in row 0",
            ],
            [
                "row" => ["Blueprint" => "XX-TEST-01", "Channel" => "AMZ", "Region" => "US", "Category" => "TEST"],
                "expectedException" => \InvalidArgumentException::class,
                "expectedMessage" => "Invalid category TEST in row 0",
            ],
            [
                "row" => ["Blueprint" => "XX-TEST-01", "Channel" => "TEST", "Region" => "US", "Category" => "A"],
                "expectedException" => \InvalidArgumentException::class,
                "expectedMessage" => "Invalid channel TEST in row 0",
            ],
            [
                "row" => ["Blueprint" => null, "Channel" => null, "Region" => null, "Category" => null],
                "expectedException" => \InvalidArgumentException::class,
                "expectedMessage" => "Row 0 () contains null value in column Blueprint",
            ]
        ];
    }

    /**
     * @param string $filePath
     * @param \DateTimeImmutable $forecastMadeInMonth
     * @param string|null $expectedException
     * @param string|null $expectedMessage
     * @param array<int>|null $expectedFsrValues
     * @param bool|null $hasAccessUnconfirmAny
     * @param bool|null $hasAccessUnconfirmEarly
     * @return void
     * @throws Exception|\PhpOffice\PhpSpreadsheet\Exception|\DateMalformedStringException
     */
    #[DataProvider('dataProviderForForecastXlsxDataProvider')]
    public function testProcessImportedForecastXlsx(string $filePath, \DateTimeImmutable $forecastMadeInMonth, ?string $expectedException = null, ?string $expectedMessage = null, ?array $expectedFsrValues = null, ?bool $hasAccessUnconfirmAny = null, ?bool $hasAccessUnconfirmEarly = null): void
    {
        if ($expectedException) {
            $this->expectException($expectedException);
            $this->expectExceptionMessage($expectedMessage);
        }
        $user = $this->retrieveOneEntityByCriteria(User::class, ['id' => 1]);

        $sfg = $this->retrieveOneEntityByCriteria(SalesForecastGroup::class, ['id' => SalesForecastManagerFixtures::getFsrGroupId1()]);
        $sfg->setForecastMadeInMonth($forecastMadeInMonth);

        foreach ($sfg->getSfDetails() as $sfDetail) {
            $this->remove($sfDetail);
        }
        $this->flushChanges();

        if ($expectedFsrValues !== null) {
            for ($i = 1; $i <= SalesForecastGroup::EXPECTED_SALES_FORECAST_DETAIL_COUNT; $i++) {
                $sfDetail = new SalesForecastDetail();
                $sfDetail->setSfGroup($sfg);
                $sfDetail->setForecastIsForMonth($forecastMadeInMonth->modify("+{$i} month"));
                $sfDetail->setOrdinal($i);
                $sfDetail->setFsr(0.0);
                $sfDetail->setDeltaPct(0);
                $sfDetail->setDeltaCalc(0);
                $sfDetail->setFsrPct(0);
                $sfDetail->setBasedOnModel(null);
                $sfDetail->setNotes('');
                $sfg->addSfDetail($sfDetail);
                $this->persist($sfDetail);
            }
            $this->flushChanges();
        }

        $request = new Request();
        $session = new Session(new MockArraySessionStorage());

        $mockSfgAdmin = $this->createPartialMock(SalesForecastGroupAdmin::class, ['hasAccessUnconfirmAny', 'hasAccessUnconfirmEarly']);
        $mockSfgAdmin->method('hasAccessUnconfirmAny')->willReturn($hasAccessUnconfirmAny);
        $mockSfgAdmin->method('hasAccessUnconfirmEarly')->willReturn($hasAccessUnconfirmEarly);
        $mockSfgAdmin->setRequest($request);
        $request->setSession($session);

        $this->manager->processImportedForecastXlsx($user, $forecastMadeInMonth, $filePath, $mockSfgAdmin);

        if ($expectedFsrValues !== null) {
            $sfg = $this->retrieveOneEntityByCriteria(SalesForecastGroup::class, ['id' => SalesForecastManagerFixtures::getFsrGroupId1()]);

            $sfDetails = $sfg->getSfDetails();
            $this->assertCount(SalesForecastGroup::EXPECTED_SALES_FORECAST_DETAIL_COUNT, $sfDetails, 'The number of SalesForecastDetails should match the expected count.');

            $index = 0;
            foreach ($sfDetails as $sfDetail) {
                $this->assertEquals($expectedFsrValues[$index], $sfDetail->getFsr(), "The FSR value for month {$sfDetail->getForecastIsForMonth()->format('Y-m')} should match the expected value of {$expectedFsrValues[$index]}.");
                $this->assertEquals(0, $sfDetail->getDeltaPct(), "The delta percentage should be 0.");
                $this->assertEquals(0, $sfDetail->getDeltaCalc(), "The delta calculation should be 0.");
                $this->assertEquals(0, $sfDetail->getFsrPct(), "The FSR percentage should be 0.");
                ++$index;
            }
        }
    }

    /**
     * @return array<string, array{
     *     filePath: string,
     *     startMonth: \DateTimeImmutable,
     *     expectedException: string|null,
     *     expectedMessage: string|null,
     *     expectedFsrValues: array<int>|null
     * }>
     */
    public function dataProviderForForecastXlsxDataProvider(): array
    {
        $startMonth = GravitiqTools::castToDateTimeImmutable('first day of this month');

        return [
            'valid file and expected values' => [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-test001.xlsx',
                'forecastMadeInMonth' => $startMonth,
                'expectedException' => null,
                'expectedMessage' => null,
                'expectedFsrValues' => [9, 8, 7, 6, 5, 4, 3, 2, 1, 0, 1, 2],
                'hasAccessUnconfirmAny' => true,
                'hasAccessUnconfirmEarly' => true,
            ],
            'invalid sales forecast details' => [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-test001.xlsx',
                'forecastMadeInMonth' => $startMonth,
                'expectedException' => \RuntimeException::class,
                'expectedMessage' => 'Sales Forecast Group does not have the expected number of Sales Forecast Details',
                'expectedFsrValues' => null,
                'hasAccessUnconfirmAny' => true,
                'hasAccessUnconfirmEarly' => true,
            ],
            'invalid header dates' => [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-test002.xlsx',
                'forecastMadeInMonth' => $startMonth,
                'expectedException' => \RuntimeException::class,
                'expectedMessage' => 'Header contains unexpected dates. Should be within +1 month of current date and +13 months in the future',
                'expectedFsrValues' => null,
                'hasAccessUnconfirmAny' => true,
                'hasAccessUnconfirmEarly' => true,
            ],
            'sales forecast group not found' => [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-test003.xlsx',
                'forecastMadeInMonth' => $startMonth,
                'expectedException' => \RuntimeException::class,
                'expectedMessage' => 'No existing Sales Forecast Group found for blueprint XX-TEST-10, channel AMZ and region UK in row 0',
                'expectedFsrValues' => null,
                'hasAccessUnconfirmAny' => true,
                'hasAccessUnconfirmEarly' => true,
            ],
            'invalid imported data' => [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-empty.xlsx',
                'forecastMadeInMonth' => $startMonth,
                'expectedException' => \RuntimeException::class,
                'expectedMessage' => 'Spreadsheet does not contain any data',
                'expectedFsrValues' => null,
                'hasAccessUnconfirmAny' => true,
                'hasAccessUnconfirmEarly' => true,
            ],
            'sfg not made in current month' => [
                'filePath' => 'tests/var/uploads/admin/ForecastSalesRate-lastMonth.xlsx',
                'forecastMadeInMonth' => GravitiqTools::castToDateTimeImmutable('first day of last month'),
                'expectedException' => \RuntimeException::class,
                'expectedMessage' => "Forecast #100 is not made in the current month. You can only edit SFGs made in the current month",
                'expectedFsrValues' => null,
                'hasAccessUnconfirmAny' => false,
                'hasAccessUnconfirmEarly' => false,
            ],
        ];
    }

    /**
     * @param array<string, mixed> $row
     * @param float|null $expectedResult
     * @param string|null $expectedException
     * @param string|null $expectedMessage
     * @return void
     */
    #[DataProvider('dataProviderForFetchSfDetailMonthlyForecastInRow')]
    public function testFetchSfDetailMonthlyForecastInRow(array $row, ?float $expectedResult, ?string $expectedException, ?string $expectedMessage): void
    {
        $sfDetail = $this->retrieveOneEntityByCriteria(SalesForecastDetail::class, ['id' => SalesForecastManagerFixtures::FSR_DETAIL_ID1]);
        $sfDetail->setForecastIsForMonth(GravitiqTools::castToDateTimeImmutable('2024-08-01'));
        if ($expectedException && $expectedMessage) {
            $this->expectException($expectedException);
            $this->expectExceptionMessage($expectedMessage);
        }
        $result = $this->invokeProtectedMethod($this->manager, 'fetchSfDetailMonthlyForecastInRow', [$sfDetail, $row, 1]);
        if ($expectedResult) {
            $this->assertEquals($expectedResult, $result, "The fetched data should match the expected result of {$expectedResult}.");
        }
    }

    /**
     * @return list<array{
     *     row: array<string, mixed>,
     *     expectedResult: float|null,
     *     expectedException: string|null,
     *     expectedMessage: string|null
     * }>
     */
    public function dataProviderForFetchSfDetailMonthlyForecastInRow(): array
    {
        return [
            [
                "row" => ["Blueprint" => "XX-TEST-01",  "Channel" => "AMZ",  "Region" => "US",  "Category" => "A",  "2024-01" => 9,  "2024-02" => 8,  "2024-03" => 7,  "2024-04" => 6,  "2024-05" => 5,  "2024-06" => 4,  "2024-07" => 3,  "2024-08" => 2,  "2024-09" => 1,  "2024-10" => 0,  "2024-11" => 1,  "2024-12" => 2],
                "expectedResult" => 2.0,
                "expectedException" => null,
                "expectedMessage" => null,
            ],
            [
                "row" => ["Blueprint" => "XX-TEST-01",  "Channel" => "AMZ",  "Region" => "US",  "Category" => "A",  "2024-01" => 9,  "2024-02" => 8,  "2024-03" => 7,  "2024-04" => 6,  "2024-05" => 5,  "2024-06" => 4,  "2024-07" => 3,  "2024-08" => "A",  "2024-09" => 1,  "2024-10" => 0,  "2024-11" => 1,  "2024-12" => 2],
                "expectedResult" => null,
                "expectedException" => \RuntimeException::class,
                "expectedMessage" => 'Invalid forecast value for 2024-08 in row 1. Should be a number',
            ],
            [
                "row" => ["Blueprint" => "XX-TEST-01",  "Channel" => "AMZ",  "Region" => "US",  "Category" => "A",  "2024-09" => 1,  "2024-10" => 0,  "2024-11" => 1,  "2024-12" => 2, "2025-01" => 3,  "2025-02" => 4,  "2025-03" => 5,  "2025-04" => 6,  "2025-05" => 7,  "2025-06" => 8,  "2025-07" => 9,  "2025-08" => 10],
                "expectedResult" => null,
                "expectedException" => \RuntimeException::class,
                "expectedMessage" => 'Row 1 does not contain a forecast value for 2024-08',
            ],
        ];
    }
}